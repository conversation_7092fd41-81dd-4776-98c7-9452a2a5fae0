using System;
using System.Threading.Tasks;
using BLL.Interfaces;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace BLL.Services
{
    public class TokenBlacklistService : ITokenBlacklistService
    {
        private readonly IMemoryCache _cache;
        private readonly ILogger<TokenBlacklistService> _logger;
        private const string BLACKLIST_PREFIX = "blacklisted_token_";

        public TokenBlacklistService(IMemoryCache cache, ILogger<TokenBlacklistService> logger)
        {
            _cache = cache;
            _logger = logger;
        }

        public async Task BlacklistTokenAsync(string tokenId, DateTime expiration)
        {
            try
            {
                if (string.IsNullOrEmpty(tokenId))
                {
                    throw new ArgumentException("Token ID cannot be null or empty", nameof(tokenId));
                }

                var cacheKey = $"{BLACKLIST_PREFIX}{tokenId}";
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpiration = expiration,
                    Priority = CacheItemPriority.Normal
                };

                _cache.Set(cacheKey, true, cacheOptions);
                
                _logger.LogInformation("Token {TokenId} has been blacklisted until {Expiration}", tokenId, expiration);
                
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error blacklisting token {TokenId}", tokenId);
                throw;
            }
        }

        public async Task<bool> IsTokenBlacklistedAsync(string tokenId)
        {
            try
            {
                if (string.IsNullOrEmpty(tokenId))
                {
                    return false;
                }

                var cacheKey = $"{BLACKLIST_PREFIX}{tokenId}";
                var isBlacklisted = _cache.TryGetValue(cacheKey, out _);
                
                await Task.CompletedTask;
                return isBlacklisted;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if token {TokenId} is blacklisted", tokenId);
                // In case of error, assume token is not blacklisted to avoid blocking valid users
                return false;
            }
        }

        public async Task CleanupExpiredTokensAsync()
        {
            try
            {
                // Memory cache automatically removes expired entries, so this is mainly for logging
                _logger.LogInformation("Token blacklist cleanup completed - expired tokens automatically removed by memory cache");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during token blacklist cleanup");
                throw;
            }
        }
    }
}
