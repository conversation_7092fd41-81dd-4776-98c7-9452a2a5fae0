using System;
using System.Threading.Tasks;

namespace BLL.Interfaces
{
    public interface ITokenBlacklistService
    {
        /// <summary>
        /// Add a token to the blacklist
        /// </summary>
        /// <param name="tokenId">The JTI (JWT ID) of the token</param>
        /// <param name="expiration">When the token expires</param>
        /// <returns>Task representing the async operation</returns>
        Task BlacklistTokenAsync(string tokenId, DateTime expiration);

        /// <summary>
        /// Check if a token is blacklisted
        /// </summary>
        /// <param name="tokenId">The JTI (JWT ID) of the token</param>
        /// <returns>True if the token is blacklisted, false otherwise</returns>
        Task<bool> IsTokenBlacklistedAsync(string tokenId);

        /// <summary>
        /// Clean up expired tokens from the blacklist
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        Task CleanupExpiredTokensAsync();
    }
}
