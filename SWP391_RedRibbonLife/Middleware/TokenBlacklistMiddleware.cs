using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using BLL.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace SWP391_RedRibbonLife.Middleware
{
    public class TokenBlacklistMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<TokenBlacklistMiddleware> _logger;

        public TokenBlacklistMiddleware(RequestDelegate next, ILogger<TokenBlacklistMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, ITokenBlacklistService tokenBlacklistService)
        {
            try
            {
                // Skip middleware for non-authenticated requests or login/logout endpoints
                if (!context.Request.Headers.ContainsKey("Authorization") || 
                    context.Request.Path.StartsWithSegments("/api/auth/login") ||
                    context.Request.Path.StartsWithSegments("/swagger"))
                {
                    await _next(context);
                    return;
                }

                var authHeader = context.Request.Headers["Authorization"].FirstOrDefault();
                if (authHeader != null && authHeader.StartsWith("Bearer "))
                {
                    var token = authHeader.Substring("Bearer ".Length).Trim();
                    
                    if (!string.IsNullOrEmpty(token))
                    {
                        try
                        {
                            var tokenHandler = new JwtSecurityTokenHandler();
                            var jsonToken = tokenHandler.ReadJwtToken(token);
                            
                            // Get the JTI (JWT ID) claim - we'll use this as our token identifier
                            var jti = jsonToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Jti)?.Value;
                            
                            // If no JTI, use a combination of user ID and issued at time as identifier
                            if (string.IsNullOrEmpty(jti))
                            {
                                var userId = jsonToken.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value;
                                var iat = jsonToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Iat)?.Value;
                                jti = $"{userId}_{iat}";
                            }

                            if (!string.IsNullOrEmpty(jti))
                            {
                                var isBlacklisted = await tokenBlacklistService.IsTokenBlacklistedAsync(jti);
                                if (isBlacklisted)
                                {
                                    _logger.LogWarning("Blocked request with blacklisted token: {TokenId}", jti);
                                    context.Response.StatusCode = 401;
                                    await context.Response.WriteAsync("Token has been revoked");
                                    return;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error processing token in blacklist middleware");
                            // Continue processing - don't block valid requests due to middleware errors
                        }
                    }
                }

                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in TokenBlacklistMiddleware");
                await _next(context);
            }
        }
    }
}
